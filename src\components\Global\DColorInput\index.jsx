import { useState, useEffect, useRef } from 'react';
import { ChromePicker } from 'react-color';

const DColorInput = ({ value, onChange, id = '' }) => {
  const [showPicker, setShowPicker] = useState(false);
  const [pickerPosition, setPickerPosition] = useState({ top: '46px', left: '0' });
  const pickerRef = useRef(null);
  const buttonRef = useRef(null);

  const calculatePickerPosition = () => {
    if (!buttonRef.current) return;

    const buttonRect = buttonRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const pickerWidth = 225; 
    const pickerHeight = 220; 

    let top = '46px';
    let left = '0';
    let transform = '';

    
    if (buttonRect.left + pickerWidth > viewportWidth) {
      left = 'auto';
      transform = 'translateX(-100%)';
     
      if (buttonRect.right - pickerWidth < 0) {
        left = '50%';
        transform = 'translateX(-50%)';
      }
    }

  
    if (buttonRect.bottom + pickerHeight > viewportHeight) {
      top = 'auto';
      transform += ' translateY(-100%)';
    }

  
    if (viewportWidth < 640) {
      left = '50%';
      top = buttonRect.height + 8 + 'px';
      transform = 'translateX(-50%)';

    
      if (buttonRect.bottom + pickerHeight > viewportHeight) {
        top = 'auto';
        transform = 'translateX(-50%) translateY(-100%)';
      }
    }

    setPickerPosition({ top, left, transform });
  };

  const togglePicker = (e) => {
    e.stopPropagation();
    if (!showPicker) {
      calculatePickerPosition();
    }
    setShowPicker(!showPicker);
  };

  const handlePickerMouseDown = (e) => {
  
    e.stopPropagation();
  };

  const handlePickerTouchStart = (e) => {
    e.stopPropagation();
  };

 
  useEffect(() => {
    const handleResize = () => {
      if (showPicker) {
        calculatePickerPosition();
      }
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }, [showPicker]);

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (pickerRef.current && !pickerRef.current.contains(e.target)) {
        setShowPicker(false);
      }
    };

    const handleEscapeKey = (e) => {
      if (e.key === 'Escape' && showPicker) {
        setShowPicker(false);
      }
    };

    // Only add the listener when the picker is open to avoid unnecessary global listeners
    if (showPicker) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('touchstart', handleClickOutside);
        document.removeEventListener('keydown', handleEscapeKey);
      };
    }
  }, [showPicker]);

  return (
    <div className="relative" data-testid={`d-color-input-${id}`}>
      {/* Button to toggle color picker */}
      <button
        ref={buttonRef}
        onClick={togglePicker}
        className="dbutton bg-grey-2 rounded-size1 p-size1 flex items-center gap-size1 w-full sm:w-auto"
        data-testid={`d-color-input-button-${id}`}
      >
        <div
          className={'rounded-size0 border-grey-5 w-5 h-5'}
          style={{ backgroundColor: value }}
          data-testid={`d-color-input-preview-${id}`}
        ></div>
        <div
          className="ml-2 text-grey-700"
          data-testid={`d-color-input-value-${id}`}
        >
          {value}
        </div>
      </button>

      {/* Color Picker */}
      {showPicker && (
        <div
          ref={pickerRef}
          className="absolute z-50 shadow-lg rounded-lg"
          data-testid={`d-color-picker-${id}`}
          onMouseDown={handlePickerMouseDown}
          onTouchStart={handlePickerTouchStart}
          style={{
            top: pickerPosition.top,
            left: pickerPosition.left,
            right: pickerPosition.left === 'auto' ? '0' : 'auto',
            bottom: pickerPosition.top === 'auto' ? '100%' : 'auto',
            transform: pickerPosition.transform || 'none',
            touchAction: 'none',
            userSelect: 'none', // Prevent text selection during drag
            maxWidth: 'calc(100vw - 32px)', // Ensure it doesn't overflow viewport
            maxHeight: 'calc(100vh - 32px)', // Ensure it doesn't overflow viewport
          }}
        >
          <div
            data-testid={`d-color-picker-hex-input-${id}`}
            className="bg-white rounded-lg overflow-hidden"
            style={{
              touchAction: 'manipulation', // Allow only essential touch gestures
              WebkitUserSelect: 'none', // Webkit prefix for user-select
              MozUserSelect: 'none', // Mozilla prefix for user-select
              msUserSelect: 'none', // IE prefix for user-select
              width: 'fit-content',
              minWidth: '225px', // ChromePicker default width
            }}
          >
            <ChromePicker
              color={value}
              onChange={onChange}
              onMouseDown={(e) => e.stopPropagation()} // Prevent event bubbling during drag
              onTouchStart={(e) => e.stopPropagation()} // Prevent touch event bubbling
              data-testid={`d-color-picker-component-${id}`}
              styles={{
                default: {
                  picker: {
                    boxShadow: 'none', // Remove default shadow since we have our own
                    borderRadius: '8px',
                    fontFamily: 'inherit',
                  },
                },
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default DColorInput;
