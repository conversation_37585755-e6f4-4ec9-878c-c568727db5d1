import { useState, useEffect, useRef } from 'react';
import { ChromePicker } from 'react-color';

const DColorInput = ({ value, onChange, id = '' }) => {
  const [showPicker, setShowPicker] = useState(false);
  const pickerRef = useRef(null);

  const togglePicker = (e) => {
    e.stopPropagation();
    setShowPicker(!showPicker);
  };

  const handlePickerMouseDown = (e) => {
    // Prevent the click outside handler from closing the picker when interacting with it
    e.stopPropagation();
  };

  const handlePickerTouchStart = (e) => {
    // Prevent touch events from bubbling up and causing page scroll
    e.stopPropagation();
  };

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (pickerRef.current && !pickerRef.current.contains(e.target)) {
        setShowPicker(false);
      }
    };

    // Only add the listener when the picker is open to avoid unnecessary global listeners
    if (showPicker) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showPicker]);

  return (
    <div className="relative" data-testid={`d-color-input-${id}`}>
      {/* Button to toggle color picker */}
      <button
        onClick={togglePicker}
        className="dbutton bg-grey-2 rounded-size1 p-size1 flex items-center gap-size1"
        data-testid={`d-color-input-button-${id}`}
      >
        <div
          className={'rounded-size0 border-grey-5 w-5 h-5'}
          style={{ backgroundColor: value }}
          data-testid={`d-color-input-preview-${id}`}
        ></div>
        <div
          className="ml-2 text-grey-700"
          data-testid={`d-color-input-value-${id}`}
        >
          {value}
        </div>
      </button>

      {/* Color Picker */}
      {showPicker && (
        <div
          ref={pickerRef}
          className="absolute top-[46px] left-0 z-50"
          data-testid={`d-color-picker-${id}`}
          onMouseDown={handlePickerMouseDown}
          onTouchStart={handlePickerTouchStart}
          style={{
            touchAction: 'none', // Prevent default touch behaviors
            userSelect: 'none', // Prevent text selection during drag
          }}
        >
          <div
            data-testid={`d-color-picker-hex-input-${id}`}
            style={{
              touchAction: 'manipulation', // Allow only essential touch gestures
              WebkitUserSelect: 'none', // Webkit prefix for user-select
              MozUserSelect: 'none', // Mozilla prefix for user-select
              msUserSelect: 'none', // IE prefix for user-select
            }}
          >
            <ChromePicker
              color={value}
              onChange={onChange}
              onMouseDown={(e) => e.stopPropagation()} // Prevent event bubbling during drag
              onTouchStart={(e) => e.stopPropagation()} // Prevent touch event bubbling
              data-testid={`d-color-picker-component-${id}`}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default DColorInput;
